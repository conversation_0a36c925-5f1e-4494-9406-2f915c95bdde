# Cloudflare Pages headers configuration

# Cache static assets for 1 year
/sounds/*
  Cache-Control: public, max-age=31536000, immutable

/backgrounds/*
  Cache-Control: public, max-age=31536000, immutable

/effects/*
  Cache-Control: public, max-age=31536000, immutable

/*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff

/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2

/*.ttf
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/ttf

/*.otf
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/otf

/*.png
  Cache-Control: public, max-age=31536000, immutable

/*.jpg
  Cache-Control: public, max-age=31536000, immutable

/*.jpeg
  Cache-Control: public, max-age=31536000, immutable

/*.gif
  Cache-Control: public, max-age=31536000, immutable

/*.svg
  Cache-Control: public, max-age=31536000, immutable

/*.ico
  Cache-Control: public, max-age=31536000, immutable

/*.mp3
  Cache-Control: public, max-age=31536000, immutable

# Cache JS and CSS for 1 year (with versioning)
/*.js
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript

/*.css
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css

# HTML files - short cache with revalidation
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Root HTML
/
  Cache-Control: public, max-age=0, must-revalidate

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Content Security Policy for enhanced security
/*
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://pagead2.googlesyndication.com https://www.google-analytics.com https://googleads.g.doubleclick.net https://tpc.googlesyndication.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://api.writeroo.net; font-src 'self' https://fonts.gstatic.com https://api.writeroo.net; img-src 'self' data: https://www.google-analytics.com https://googleads.g.doubleclick.net https://tpc.googlesyndication.com; connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com; frame-src https://googleads.g.doubleclick.net https://tpc.googlesyndication.com;
