# Cloudflare Pages redirects for client-side routing
# This ensures that all routes are handled by the React app

# Handle client-side routing - redirect all non-file requests to index.html
/*    /index.html   200

# Specific routes for better SEO (optional)
/privacy    /index.html   200
/terms      /index.html   200

# Ensure static assets are served correctly
/sounds/*   /sounds/:splat   200
/backgrounds/*   /backgrounds/:splat   200
/effects/*   /effects/:splat   200
/*.css   /:splat   200
/*.js    /:splat   200
/*.png   /:splat   200
/*.jpg   /:splat   200
/*.jpeg  /:splat   200
/*.gif   /:splat   200
/*.svg   /:splat   200
/*.ico   /:splat   200
/*.woff  /:splat   200
/*.woff2 /:splat   200
/*.ttf   /:splat   200
/*.otf   /:splat   200
/*.mp3   /:splat   200
