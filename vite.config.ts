import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import { VitePluginRadar } from "vite-plugin-radar";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    VitePluginRadar({
      analytics: {
        id: "G-5EE9JSJFPR",
      },
    }),
  ],
  build: {
    // Optimize for Cloudflare Pages
    target: 'es2015',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          analytics: ['@vercel/analytics'],
        },
      },
    },
    // Ensure assets are properly handled
    assetsDir: 'assets',
    copyPublicDir: true,
  },
  // Ensure proper base path for Cloudflare Pages
  base: '/',
  // Optimize dev server
  server: {
    port: 5175,
    host: true,
  },
  // Preview server config
  preview: {
    port: 4173,
    host: true,
  },
});
