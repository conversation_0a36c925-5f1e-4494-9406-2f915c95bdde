{"headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).woff2", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "font/woff2"}]}, {"source": "/(.*).woff", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "font/woff"}]}, {"source": "/(.*).ttf", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "font/ttf"}]}, {"source": "/(.*).otf", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "font/otf"}]}]}