{"name": "invincible", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:cloudflare": "npm run build && npm run post-build", "post-build": "node scripts/post-build.js", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build:cloudflare && wrangler pages deploy dist", "deploy:preview": "npm run build:cloudflare && wrangler pages deploy dist --env preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.2", "@vercel/analytics": "^1.5.0", "dom-to-image": "^2.6.0", "glsl-canvas-js": "^0.2.9", "html2canvas-pro": "^1.5.8", "prettier": "^3.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/dom-to-image": "^2.6.7", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-radar": "^0.10.0"}, "patchedDependencies": {"glsl-canvas-js@0.2.9": "patches/<EMAIL>"}}