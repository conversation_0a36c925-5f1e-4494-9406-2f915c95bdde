# Cloudflare Pages Deployment Guide

This project is optimized for deployment on Cloudflare Pages with full support for client-side routing, Google Analytics, Google AdSense, and audio playback.

## Quick Deployment

### Option 1: Automatic Deployment (Recommended)

1. **Connect to Cloudflare Pages:**
   - Go to [Cloudflare Pages](https://pages.cloudflare.com/)
   - Click "Create a project"
   - Connect your Git repository
   - Select this repository

2. **Build Configuration:**
   ```
   Build command: npm run build
   Build output directory: dist
   Root directory: (leave empty)
   Environment variables: NODE_VERSION=18
   ```

3. **Deploy:**
   - Click "Save and Deploy"
   - Cloudflare will automatically build and deploy your site

### Option 2: Manual Deployment

1. **Install Wrangler CLI:**
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare:**
   ```bash
   wrangler login
   ```

3. **Build and Deploy:**
   ```bash
   npm run deploy
   ```

## Configuration Details

### Client-Side Routing
- `public/_redirects` handles SPA routing for `/`, `/privacy`, `/terms`
- All routes fallback to `index.html` for proper React Router functionality

### Caching Strategy
- Static assets (images, fonts, audio): 1 year cache
- JS/CSS bundles: 1 year cache with immutable flag
- HTML files: No cache with revalidation

### Security Headers
- CSP configured for Google Analytics and AdSense
- XSS protection and frame options enabled
- Secure referrer policy

### Performance Optimizations
- Code splitting for vendor libraries
- Terser minification
- Asset optimization
- Lazy loading for third-party scripts

## Environment Variables

Set these in Cloudflare Pages dashboard:

```
NODE_ENV=production
VITE_ENVIRONMENT=production
```

## Third-Party Services

### Google Analytics
- Tracking ID: `G-ZSDD10FT37`
- Lazy loaded after 2 seconds
- Only loads in production environment

### Google AdSense
- Publisher ID: `ca-pub-****************`
- Lazy loaded after 3 seconds
- Only loads in production environment

## Verification Checklist

After deployment, verify:

- [ ] Homepage loads correctly
- [ ] `/privacy` and `/terms` routes work
- [ ] Audio button plays sound
- [ ] Google Analytics tracking works
- [ ] AdSense ads display (may take time to approve)
- [ ] All static assets load properly
- [ ] Mobile responsiveness works

## Troubleshooting

### Common Issues

1. **404 on refresh:**
   - Check `_redirects` file is in `dist` folder
   - Verify Cloudflare Pages routing configuration

2. **Assets not loading:**
   - Check `_headers` file for proper cache configuration
   - Verify asset paths in build output

3. **Analytics not working:**
   - Confirm `NODE_ENV=production` is set
   - Check browser console for script loading errors

4. **Audio not playing:**
   - Verify `sounds/hmm.mp3` exists in `dist` folder
   - Check browser console for audio loading errors

### Debug Commands

```bash
# Build locally and test
npm run build
npm run preview

# Check build output
ls -la dist/
cat dist/_redirects
cat dist/_headers

# Verify deployment info
cat dist/deployment-info.json
```

## Custom Domain

To use a custom domain:

1. Add domain in Cloudflare Pages dashboard
2. Update DNS records as instructed
3. Enable SSL/TLS encryption
4. Configure redirects if needed

## Performance Monitoring

Monitor your deployment:
- Cloudflare Analytics dashboard
- Google Analytics for user behavior
- Core Web Vitals in Google Search Console
