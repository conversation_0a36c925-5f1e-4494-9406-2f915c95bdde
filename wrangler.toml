# Cloudflare Pages configuration
name = "invincible-title-card-maker"
compatibility_date = "2024-01-01"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.environment]
NODE_VERSION = "18"

# Pages configuration
[[pages_build_output_dir]]
directory = "dist"

# Environment variables for production
[env.production]
NODE_ENV = "production"
VITE_ENVIRONMENT = "production"

# Environment variables for preview
[env.preview]
NODE_ENV = "production"
VITE_ENVIRONMENT = "preview"
