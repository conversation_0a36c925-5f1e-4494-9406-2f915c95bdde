export function CTA() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-r from-yellow-500 to-yellow-600">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
            Design Your Invincible Title Card Today
          </h2>
          <p className="text-xl text-black/80 mb-8 leading-relaxed">
            Create stunning title card graphics with our free online editor.
            No registration required, no downloads needed - start designing immediately in your browser.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a
              href="#editor"
              className="bg-black hover:bg-slate-800 text-yellow-400 font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-flex items-center gap-2 hover-lift"
            >
              <i className="fas fa-rocket"></i>
              Start Designing
            </a>

            <a
              href="#features"
              className="border-2 border-black text-black hover:bg-black hover:text-yellow-400 font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-flex items-center gap-2"
            >
              <i className="fas fa-info-circle"></i>
              Learn More
            </a>
          </div>

          <div className="mt-8 flex flex-wrap justify-center items-center gap-6 text-black/70">
            <div className="flex items-center gap-2">
              <i className="fas fa-check-circle text-black"></i>
              <span className="text-sm font-medium">Completely Free</span>
            </div>
            <div className="flex items-center gap-2">
              <i className="fas fa-check-circle text-black"></i>
              <span className="text-sm font-medium">No Registration</span>
            </div>
            <div className="flex items-center gap-2">
              <i className="fas fa-check-circle text-black"></i>
              <span className="text-sm font-medium">HD Export</span>
            </div>
            <div className="flex items-center gap-2">
              <i className="fas fa-check-circle text-black"></i>
              <span className="text-sm font-medium">All Devices</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
