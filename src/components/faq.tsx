import { useState } from "react";

export function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: "What is the Invincible Title Card Maker?",
      answer: "Our Invincible title card creator is a free web-based design tool that helps you generate custom title cards inspired by the popular animated series. Design professional-looking graphics with customizable text, colors, and backgrounds."
    },
    {
      question: "Is this title card maker free to use?",
      answer: "Yes, our Invincible title card tool is completely free. Create unlimited designs and download high-quality PNG files without any subscription fees or account requirements."
    },
    {
      question: "Can I use these title card designs commercially?",
      answer: "Generated title cards are suitable for personal and non-commercial use. For commercial projects, please note that 'Invincible' is a registered trademark, and appropriate licensing may be required. Check our Terms of Service for details."
    },
    {
      question: "Can I use this tool on my mobile device?",
      answer: "Yes, the Invincible Title Card Maker features a responsive design that works on smartphones, tablets, and desktop computers. The interface automatically adjusts to fit your screen size."
    },
    {
      question: "How do I change font size and colors?",
      answer: "In the editor interface, you can use the sliders and color pickers in the control panel to adjust text size, colors, and outlines. Changes are displayed in real-time in the preview area."
    },
    {
      question: "What resolution are the generated images?",
      answer: "By default, the generated title cards are 1920 x 1080 pixels (full HD), which is suitable for most modern display devices and social media platforms."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="py-16 md:py-24 bg-slate-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-slate-300 max-w-5xl mx-auto">
            Get answers about creating Invincible title card designs with our online tool
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <div 
              key={index}
              className="mb-4 bg-slate-800 rounded-lg overflow-hidden"
            >
              <button
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-slate-750 transition-colors"
                onClick={() => toggleFAQ(index)}
              >
                <h3 className="text-lg font-semibold pr-4">{faq.question}</h3>
                <i className={`fas fa-chevron-${openIndex === index ? 'up' : 'down'} text-yellow-500 transition-transform`}></i>
              </button>
              
              {openIndex === index && (
                <div className="px-6 pb-4">
                  <p className="text-slate-300 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
        
      </div>
    </section>
  );
}
