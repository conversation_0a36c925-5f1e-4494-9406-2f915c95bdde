import { useEffect } from 'react';

export function AdSense() {
  useEffect(() => {
    // 只在生产环境加载 Google AdSense
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    // 懒加载 Google AdSense
    const loadGoogleAdSense = () => {
      // 检查是否已经加载过
      if (document.querySelector('script[src*="adsbygoogle.js"]')) {
        return;
      }

      // 创建 AdSense 脚本
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************';
      script.crossOrigin = 'anonymous';
      document.head.appendChild(script);

      // 添加 meta 标签
      const meta = document.createElement('meta');
      meta.name = 'google-adsense-account';
      meta.content = 'ca-pub-****************';
      document.head.appendChild(meta);
    };

    // 延迟加载以避免影响初始页面性能
    const timer = setTimeout(loadGoogleAdSense, 3000);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return null;
}
