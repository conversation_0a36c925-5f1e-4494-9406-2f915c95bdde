import React, { useEffect, useRef } from "react";
import { Canvas } from "glsl-canvas-js";
import fragment from "./title.frag?raw";

interface ShaderTextProps {
  text: string;
  color: string;
  fontSize: number;
  outlineColor?: string;
  outline?: number;
  width: number;
}

const Title: React.FC<ShaderTextProps> = ({
  text,
  color,
  fontSize,
  outlineColor = "transparent",
  outline = 0,
  width,
}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  useEffect(() => {
    async function updateCanvas() {
      if (!canvasRef.current) return;

      const canvas = canvasRef.current;
      const pixelRatio = window.devicePixelRatio || 1;

      // Calculate proper dimensions
      const canvasWidth = width;
      const canvasHeight = fontSize * 1.2;

      // Set canvas dimensions
      canvas.width = canvasWidth * pixelRatio;
      canvas.height = canvasHeight * pixelRatio;
      canvas.style.width = `${canvasWidth}px`;
      canvas.style.height = `${canvasHeight}px`;

      // Create offscreen canvas for text rendering
      const textCanvas = document.createElement("canvas");
      const scaledWidth = width * pixelRatio;
      const scaledFontSize = fontSize * pixelRatio;

      // Set the actual canvas sizes in pixels
      textCanvas.width = scaledWidth;
      textCanvas.height = scaledFontSize;

      const ctx = textCanvas.getContext("2d", { alpha: true });
      if (!ctx) return;

      // Clear canvas
      ctx.clearRect(0, 0, textCanvas.width, textCanvas.height);

      // Scale all drawing operations
      ctx.scale(pixelRatio, pixelRatio);

      // Set text properties
      ctx.font = `${fontSize}px "Woodblock"`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      const centerX = width / 2;
      const centerY = fontSize / 2;

      // Draw the text
      ctx.fillStyle = color;
      ctx.fillText(text, centerX, centerY);

      // Draw the outline on top of the text
      if (outline > 0 && outlineColor !== "transparent") {
        ctx.lineWidth = outline;
        ctx.strokeStyle = outlineColor;
        ctx.lineJoin = "round";
        ctx.miterLimit = 2;
        ctx.strokeText(text, centerX, centerY);
      }

      try {
        // Set up WebGL shader
        const gl = new Canvas(canvas, {
          fragmentString: fragment,
          preserveDrawingBuffer: true, // Allow to copy the canvas when using toDataURL
        });

        // Pass the text canvas to the shader
        gl.setTexture("u_texture", textCanvas, {
          UNPACK_PREMULTIPLY_ALPHA_WEBGL: 1, // Makes text look less aliased
        });
      } catch (error) {
        console.warn("WebGL not supported, falling back to 2D canvas:", error);
        // Fallback to 2D canvas rendering
        const ctx2d = canvas.getContext("2d");
        if (ctx2d) {
          // Scale the context to match pixel ratio
          ctx2d.scale(pixelRatio, pixelRatio);
          ctx2d.clearRect(0, 0, canvasWidth, canvasHeight);
          ctx2d.drawImage(textCanvas, 0, 0, canvasWidth, canvasHeight);
        }
      }
    }

    // Wait for font to load before rendering
    document.fonts.ready.then(() => {
      updateCanvas();
    });
  }, [text, color, fontSize, outline, outlineColor, width]);

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-auto block"
      style={{
        maxWidth: '100%',
        height: 'auto',
        display: 'block'
      }}
    />
  );
};

export default Title;
