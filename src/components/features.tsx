export function Features() {
  const features = [
    {
      icon: "fas fa-palette",
      title: "Highly Customizable",
      description: "Personalize every aspect of your Invincible title card with custom text, fonts, colors, and positioning options."
    },
    {
      icon: "fas fa-image",
      title: "Various Backgrounds & Effects",
      description: "Select from multiple background templates and visual effects to enhance your title card designs with authentic styling."
    },
    {
      icon: "fas fa-download",
      title: "High-Quality Export",
      description: "Download your finished title card as a high-resolution PNG image, ready for videos, social media, or printing."
    },
    {
      icon: "fas fa-mobile-alt",
      title: "Responsive Design",
      description: "Works on smartphones, tablets, and desktop computers. The interface automatically adjusts to fit your screen size."
    },
    {
      icon: "fas fa-bolt",
      title: "Instant Preview",
      description: "Real-time preview of your design changes with a what-you-see-is-what-you-get editing experience."
    },
    {
      icon: "fas fa-heart",
      title: "Completely Free",
      description: "No registration required, no payment needed, unlimited creation and download of your title cards."
    }
  ];

  return (
    <section id="features" className="py-16 md:py-24 bg-slate-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Invincible Title Card Features
          </h2>
          <p className="text-xl text-slate-300 max-w-5xl mx-auto">
            Create professional Invincible title card designs with powerful yet simple tools for customization and editing
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-slate-800 rounded-xl p-6 hover:bg-slate-750 transition-colors group"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-400 transition-colors">
                  <i className={`${feature.icon} text-black text-xl`}></i>
                </div>
                <h3 className="text-xl font-semibold">{feature.title}</h3>
              </div>
              <p className="text-slate-300 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
