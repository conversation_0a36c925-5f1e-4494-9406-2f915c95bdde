export function About() {
  return (
    <section id="about" className="py-16 md:py-24 bg-slate-950">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              About Invincible Title Cards
            </h2>
            <p className="text-xl text-slate-300 max-w-5xl mx-auto">
              Discover the distinctive visual design elements that make Invincible title card graphics so iconic
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-6">About Invincible</h3>
              <p className="text-slate-300 leading-relaxed mb-6">
                Invincible is a critically acclaimed animated series featuring distinctive title card designs that have become instantly recognizable. These bold, stylized graphics perfectly capture the show's dynamic energy and visual identity.
              </p>
              <p className="text-slate-300 leading-relaxed mb-6">
                Our online title card maker empowers fans and content creators to design their own Invincible-inspired graphics. Perfect for video projects, social media content, or personal artwork, this tool brings professional design capabilities to everyone.
              </p>
              <p className="text-slate-300 leading-relaxed">
                Create authentic-looking title card designs with customizable text, colors, and backgrounds. Our browser-based editor requires no downloads or design experience, making it accessible to creators of all skill levels.
              </p>
            </div>
            
            <div className="bg-slate-800 rounded-xl p-8">
              <h3 className="text-2xl font-bold mb-6">Tool Features</h3>
              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">Completely free, no registration required</span>
                </li>
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">Runs in browser, no download needed</span>
                </li>
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">Real-time preview and editing</span>
                </li>
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">High-definition PNG export</span>
                </li>
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">Responsive design, supports all devices</span>
                </li>
                <li className="flex items-start gap-3">
                  <i className="fas fa-check-circle text-yellow-500 mt-1"></i>
                  <span className="text-slate-300">Multiple preset themes and custom options</span>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="text-center mt-16">
            <h3 className="text-2xl font-bold mb-6">Ready to Design Your Invincible Title Card?</h3>
            <p className="text-xl text-slate-300 mb-8">
              Start creating professional title card graphics with our free online design tool
            </p>
            <a
              href="#editor"
              className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-flex items-center gap-2"
            >
              <i className="fas fa-rocket"></i>
              Start Designing
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
