export function HowTo() {
  const steps = [
    {
      number: "1",
      title: "Choose a Style",
      description: "Browse our collection of Invincible title card templates or start with a blank canvas to create your unique design.",
      icon: "fas fa-palette"
    },
    {
      number: "2",
      title: "Customize Text",
      description: "Add your custom text, adjust fonts, colors, and positioning to match the iconic Invincible title card aesthetic.",
      icon: "fas fa-edit"
    },
    {
      number: "3",
      title: "Download Result",
      description: "Export your completed title card as a high-resolution PNG file, perfect for videos, presentations, or social sharing.",
      icon: "fas fa-download"
    }
  ];

  return (
    <section id="how-to-use" className="py-16 md:py-24 bg-slate-950">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            How to Create Your Invincible Title Card
          </h2>
          <p className="text-xl text-slate-300 max-w-5xl mx-auto">
            Follow these three easy steps to design your custom title card graphics
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`flex flex-col md:flex-row items-center gap-8 mb-16 ${
                index % 2 === 1 ? 'md:flex-row-reverse' : ''
              }`}
            >
              <div className="flex-1">
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mr-4 text-black font-bold text-2xl">
                    {step.number}
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold">{step.title}</h3>
                </div>
                <p className="text-lg text-slate-300 leading-relaxed">
                  {step.description}
                </p>
              </div>

              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center">
                  <i className={`${step.icon} text-black text-4xl`}></i>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <a
            href="#editor"
            className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-flex items-center gap-2"
          >
            <i className="fas fa-play"></i>
            Start Creating Now
          </a>
        </div>
      </div>
    </section>
  );
}