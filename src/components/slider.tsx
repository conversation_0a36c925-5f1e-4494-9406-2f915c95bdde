interface SliderProps {
  value: number;
  min: number;
  max: number;
  step?: number;
  onChange: (value: number) => void;
  label: string;
  id?: string;
}

export function Slider(props: SliderProps) {
  const { value, min, max, step = 1, onChange, label, id } = props;
  const sliderId = id || `slider-${label.toLowerCase().replace(/\s+/g, '-')}`;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label htmlFor={sliderId} className="text-sm font-medium text-slate-200">
          {label}
        </label>
        <div className="text-sm text-slate-400 bg-slate-700 px-2 py-1 rounded min-w-[3rem] text-center">
          {value}
        </div>
      </div>
      <input
        id={sliderId}
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseInt(e.target.value, 10))}
        className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
        aria-label={`${label}: ${value}`}
        aria-valuemin={min}
        aria-valuemax={max}
        aria-valuenow={value}
      />
      <style>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #eab308;
          cursor: pointer;
          border: 2px solid #1e293b;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .slider::-webkit-slider-thumb:hover {
          background: #facc15;
          transform: scale(1.1);
        }

        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #eab308;
          cursor: pointer;
          border: 2px solid #1e293b;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .slider:focus {
          outline: 2px solid #eab308;
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
}
