export function Testimonials() {
  const testimonials = [
    {
      rating: 5,
      text: "This tool is fantastic! I used it to create professional-grade title cards for my fan edit videos, and my friends thought they were officially made. The interface is simple and intuitive, yet powerful.",
      author: "<PERSON>",
      role: "Video Creator"
    },
    {
      rating: 5,
      text: "As a fan of Invincible, I've always wanted to create similar title effects. This maker let me do it in minutes, and I could add my own colors and effects. Highly recommended!",
      author: "<PERSON>",
      role: "Animation Enthusiast"
    },
    {
      rating: 5,
      text: "Completely free and fully featured, the generated image quality is excellent. The responsive design lets me use it easily on my phone too. This is the best title card maker I've seen.",
      author: "<PERSON>",
      role: "Social Media Creator"
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <i
        key={index}
        className={`fas fa-star ${
          index < rating ? 'text-yellow-400' : 'text-slate-600'
        }`}
      />
    ));
  };

  return (
    <section className="py-16 md:py-24 bg-slate-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            What Users Say
          </h2>
          <p className="text-xl text-slate-300 max-w-5xl mx-auto">
            See what other users think about our tool
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-slate-900 rounded-xl p-6 hover:bg-slate-850 transition-colors"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {renderStars(testimonial.rating)}
              </div>
              
              {/* Quote */}
              <blockquote className="text-slate-300 leading-relaxed mb-6 italic">
                "{testimonial.text}"
              </blockquote>
              
              {/* Author */}
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-black font-bold text-lg">
                    {testimonial.author.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-semibold text-slate-200">
                    {testimonial.author}
                  </div>
                  <div className="text-sm text-slate-400">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* <div className="text-center mt-12">
          <p className="text-slate-300 mb-4">
            Want to share your experience?
          </p>
          <a
            href="mailto:<EMAIL>"
            className="text-yellow-500 hover:text-yellow-400 font-semibold transition-colors"
          >
            Contact us
          </a>
        </div> */}
      </div>
    </section>
  );
}
