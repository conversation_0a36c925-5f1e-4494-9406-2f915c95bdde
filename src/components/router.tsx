import { useState, useEffect } from 'react';
import Header from './header';
import { Footer } from './footer';
import { Home } from './home';
import { Privacy } from './privacy';
import { Terms } from './terms';

export function AppRouter() {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const renderPage = () => {
    switch (currentPath) {
      case '/privacy':
        return <Privacy />;
      case '/terms':
        return <Terms />;
      default:
        return <Home />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      {renderPage()}
      <Footer />
    </div>
  );
}
