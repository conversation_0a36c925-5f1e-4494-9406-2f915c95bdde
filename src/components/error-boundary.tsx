import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  resetError: () => void;
}

function DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-950 px-4">
      <div className="max-w-md w-full bg-slate-900 rounded-xl p-8 text-center border border-slate-800">
        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <i className="fas fa-exclamation-triangle text-white text-2xl" />
        </div>
        
        <h1 className="text-2xl font-bold text-slate-100 mb-4">
          出现了一些问题
        </h1>
        
        <p className="text-slate-300 mb-6 leading-relaxed">
          很抱歉，应用程序遇到了意外错误。请尝试刷新页面或联系支持团队。
        </p>
        
        {error && (
          <details className="mb-6 text-left">
            <summary className="cursor-pointer text-slate-400 hover:text-slate-300 text-sm mb-2">
              查看错误详情
            </summary>
            <pre className="text-xs text-red-400 bg-slate-800 p-3 rounded overflow-auto max-h-32">
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </pre>
          </details>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={resetError}
            className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold py-3 px-4 rounded-lg transition-colors"
          >
            重试
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="flex-1 bg-slate-700 hover:bg-slate-600 text-slate-100 font-semibold py-3 px-4 rounded-lg transition-colors"
          >
            刷新页面
          </button>
        </div>
        
        <p className="text-xs text-slate-500 mt-6">
          如果问题持续存在，请联系
          <a href="mailto:<EMAIL>" className="text-yellow-400 hover:text-yellow-300 ml-1">
            技术支持
          </a>
        </p>
      </div>
    </div>
  );
}
