import { useEffect } from 'react';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

export function Analytics() {
  useEffect(() => {
    // 只在生产环境加载 Google Analytics
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    // 懒加载 Google Analytics
    const loadGoogleAnalytics = () => {
      // 创建 gtag 脚本
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-ZSDD10FT37';
      document.head.appendChild(script);

      // 初始化 dataLayer 和 gtag 函数
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag(...args: any[]) {
        window.dataLayer.push(args);
      };

      // 配置 Google Analytics
      window.gtag('js', new Date());
      window.gtag('config', 'G-ZSDD10FT37', {
        // 优化性能设置
        send_page_view: true,
        anonymize_ip: true,
      });
    };

    // 延迟加载以避免影响初始页面性能
    const timer = setTimeout(loadGoogleAnalytics, 2000);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return null;
}
