import { createNavigationHandler } from '../utils/navigation';

export function Footer() {
  const quickLinks = [
    { name: "Features", href: "#features" },
    { name: "How to Use", href: "#how-to-use" },
    { name: "FAQ", href: "#faq" },
    { name: "About", href: "#about" }
  ];

  const otherStuff = [
    {
      name: "MeMe-Generator",
      icon: "/products/meme-logo.png",
      link: "https://meme-generator.app/",
    },
    {
      name: "<PERSON>ya<PERSON>",
      icon: "/products/sybau-logo.png",
      link: "https://sybau.app/",
    },
  ];

  return (
    <footer className="bg-slate-900 border-t border-slate-800 mt-auto">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <div className="flex items-center gap-3 mb-4">
              <img src="/logo.png" alt="Invincible Title Card Maker Logo" className="h-12 w-12" />
              <div className="text-lg font-bold">
                Invincible Title Card Maker
              </div>
            </div>
            <p className="text-slate-400 text-sm leading-relaxed mb-4">
              Design professional Invincible title card graphics with our free online tool. Create custom title cards with personalized text, colors, and backgrounds inspired by the animated series.
            </p>
            <div className="text-xs text-slate-500">
              This website is not affiliated with Invincible or Amazon Prime Video. All trademarks and copyrights belong to their respective owners.
            </div>
          </div>

          {/* Quick Links */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4 text-slate-200">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-slate-400 hover:text-yellow-400 transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Other Projects */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4 text-slate-200">Other Projects</h3>
            <div className="flex items-center gap-3">
              {otherStuff.map((product) => (
                <a
                  href={product.link}
                  key={product.name}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover-lift grayscale hover:grayscale-0 transition-all"
                  title={product.name}
                >
                  <img
                    src={product.icon}
                    alt={product.name}
                    className="h-10 w-10 rounded-lg"
                  />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-slate-500 mb-4 md:mb-0">
            © 2025 Invincible Title Card Maker. All rights reserved.
          </div>
          <div className="flex items-center gap-6 text-sm">
            <a
              href="/privacy"
              className="text-slate-400 hover:text-yellow-400 transition-colors"
              onClick={createNavigationHandler('/privacy')}
            >
              Privacy Policy
            </a>
            <a
              href="/terms"
              className="text-slate-400 hover:text-yellow-400 transition-colors"
              onClick={createNavigationHandler('/terms')}
            >
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
