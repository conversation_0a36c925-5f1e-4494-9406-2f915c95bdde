interface CheckBoxProps {
  value: boolean;
  onChange: (value: boolean) => void;
  label: React.ReactNode;
  id?: string;
  disabled?: boolean;
}

export function CheckBox(props: CheckBoxProps) {
  const { value, onChange, label, id, disabled = false } = props;
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="flex items-center">
      <label htmlFor={checkboxId} className="flex items-center cursor-pointer gap-3 group">
        <div className="relative">
          <input
            id={checkboxId}
            type="checkbox"
            className="sr-only"
            checked={value}
            onChange={(e) => onChange(e.target.checked)}
            disabled={disabled}
            aria-describedby={`${checkboxId}-description`}
          />
          <div className={`
            w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center
            ${value
              ? 'bg-yellow-500 border-yellow-500'
              : 'bg-slate-700 border-slate-600 group-hover:border-yellow-400'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            focus-within:ring-2 focus-within:ring-yellow-400 focus-within:ring-offset-2 focus-within:ring-offset-slate-900
          `}>
            {value && (
              <i className="fas fa-check text-black text-xs" aria-hidden="true" />
            )}
          </div>
        </div>
        <span className={`text-sm font-medium select-none ${disabled ? 'text-slate-500' : 'text-slate-200 group-hover:text-slate-100'}`}>
          {label}
        </span>
      </label>
    </div>
  );
}
