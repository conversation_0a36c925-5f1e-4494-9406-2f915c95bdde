declare module 'glsl-canvas-js' {
  interface CanvasOptions {
    fragmentString?: string;
    preserveDrawingBuffer?: boolean;
    [key: string]: any;
  }

  interface TextureOptions {
    [key: string]: any;
  }

  export class Canvas {
    constructor(element: HTMLCanvasElement, options?: CanvasOptions);
    load(fragmentShader: string): void;
    setUniform(name: string, value: any): void;
    setTexture(name: string, texture: HTMLCanvasElement | HTMLImageElement, options?: TextureOptions): void;
    render(): void;
    destroy(): void;
  }
}
