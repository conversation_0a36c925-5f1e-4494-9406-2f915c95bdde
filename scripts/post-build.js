#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Running post-build optimizations for Cloudflare Pages...');

const distDir = path.join(__dirname, '..', 'dist');

// Ensure _redirects and _headers are in the dist directory
const publicFiles = ['_redirects', '_headers'];

publicFiles.forEach(file => {
  const srcPath = path.join(__dirname, '..', 'public', file);
  const destPath = path.join(distDir, file);
  
  if (fs.existsSync(srcPath)) {
    fs.copyFileSync(srcPath, destPath);
    console.log(`✅ Copied ${file} to dist directory`);
  } else {
    console.warn(`⚠️  ${file} not found in public directory`);
  }
});

// Verify critical files exist
const criticalFiles = [
  'index.html',
  'sounds/hmm.mp3'
];

criticalFiles.forEach(file => {
  const filePath = path.join(distDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ Verified ${file} exists`);
  } else {
    console.error(`❌ Critical file missing: ${file}`);
    process.exit(1);
  }
});

// Check if Google Analytics and AdSense scripts are properly configured
const indexPath = path.join(distDir, 'index.html');
if (fs.existsSync(indexPath)) {
  const indexContent = fs.readFileSync(indexPath, 'utf8');
  
  // Verify meta tags
  if (indexContent.includes('google-adsense-account')) {
    console.log('✅ Google AdSense meta tag found');
  } else {
    console.warn('⚠️  Google AdSense meta tag not found');
  }
  
  // Verify structured data
  if (indexContent.includes('application/ld+json')) {
    console.log('✅ Structured data found');
  } else {
    console.warn('⚠️  Structured data not found');
  }
}

// Create a deployment info file
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
const deployInfo = {
  buildTime: new Date().toISOString(),
  environment: process.env.NODE_ENV || 'production',
  version: packageJson.version,
  features: {
    googleAnalytics: true,
    googleAdSense: true,
    clientSideRouting: true,
    audioPlayback: true
  }
};

fs.writeFileSync(
  path.join(distDir, 'deployment-info.json'),
  JSON.stringify(deployInfo, null, 2)
);

console.log('✅ Post-build optimizations completed successfully!');
console.log('📦 Ready for Cloudflare Pages deployment');
